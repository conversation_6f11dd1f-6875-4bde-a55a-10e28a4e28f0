<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crafting Bench</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app" class="hidden">
        <div class="crafting-container">
            <div class="header">
                <h1 id="menu-title">Bench Crafting</h1>
                <button id="close-btn" class="close-button">&times;</button>
            </div>
            
            <div class="content">
                <div class="categories">
                    <button class="category-btn active" data-category="guns">Weapons</button>
                </div>
                
                <div class="items-container">
                    <!-- Weapons Category -->
                    <div id="guns-content" class="category-content active">
                        <div class="items-grid" id="guns-grid">
                            <!-- Dynamically populated -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Item Detail Modal -->
    <div id="item-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Item Details</h2>
                <button id="modal-close" class="close-button">&times;</button>
            </div>
            <div class="modal-body">
                <div id="modal-requirements" class="requirements">
                    <!-- Dynamically populated -->
                </div>
                <div class="modal-actions">
                    <button id="modal-action-btn" class="action-button">Craft Weapon</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="app.js"></script>
</body>
</html>
