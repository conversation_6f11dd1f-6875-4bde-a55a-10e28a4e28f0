<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crafting Bench</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app" class="hidden">
        <div class="crafting-container">
            <div class="header">
                <h1 id="menu-title">Bench Crafting</h1>
                <button id="close-btn" class="close-button">&times;</button>
            </div>
            
            <div class="content">
                <div class="categories">
                    <button class="category-btn active" data-category="guns">Guns</button>
                    <button class="category-btn" data-category="components">Components</button>
                    <button class="category-btn" data-category="ammo">Ammo</button>
                    <button class="category-btn" data-category="attachments">Attachments</button>
                </div>
                
                <div class="items-container">
                    <!-- Guns Category -->
                    <div id="guns-content" class="category-content active">
                        <div class="items-grid" id="guns-grid">
                            <!-- Dynamically populated -->
                        </div>
                    </div>
                    
                    <!-- Components Category -->
                    <div id="components-content" class="category-content">
                        <div class="items-grid" id="components-grid">
                            <!-- Dynamically populated -->
                        </div>
                    </div>
                    
                    <!-- Ammo Category -->
                    <div id="ammo-content" class="category-content">
                        <div class="items-grid" id="ammo-grid">
                            <div class="placeholder">
                                <p>Ammo crafting coming soon...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Attachments Category -->
                    <div id="attachments-content" class="category-content">
                        <div class="items-grid" id="attachments-grid">
                            <div class="placeholder">
                                <p>Attachment crafting coming soon...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Item Detail Modal -->
    <div id="item-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Item Details</h2>
                <button id="modal-close" class="close-button">&times;</button>
            </div>
            <div class="modal-body">
                <div id="modal-requirements" class="requirements">
                    <!-- Dynamically populated -->
                </div>
                <div class="modal-actions">
                    <div class="quantity-selector">
                        <label for="quantity">Quantity:</label>
                        <input type="number" id="quantity" min="1" max="50" value="1">
                    </div>
                    <button id="modal-action-btn" class="action-button">Craft</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="app.js"></script>
</body>
</html>
