-- Server-side logging system for qb-crafting

local QBCore = exports['qb-core']:GetCoreObject()

-- Log levels
local LOG_LEVELS = {
    INFO = 'INFO',
    WARN = 'WARN',
    ERROR = 'ERROR',
    DEBUG = 'DEBUG'
}

-- Log an event to console and optionally to database
function Log(eventType, src, data, level)
    if not Config.Global.logEvents then return end
    
    level = level or LOG_LEVELS.INFO
    local timestamp = os.date('%Y-%m-%d %H:%M:%S')
    local Player = QBCore.Functions.GetPlayer(src)
    local playerInfo = Player and {
        citizenid = Player.PlayerData.citizenid,
        name = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname,
        job = Player.PlayerData.job.name
    } or { citizenid = 'unknown', name = 'unknown', job = 'unknown' }
    
    local logEntry = {
        timestamp = timestamp,
        level = level,
        event = eventType,
        player = playerInfo,
        data = data,
        source = src
    }
    
    -- Console logging
    local logMessage = string.format('[%s] [%s] qb-crafting: %s - Player: %s (%s) - Data: %s', 
        timestamp, level, eventType, playerInfo.name, playerInfo.citizenid, json.encode(data))
    
    if level == LOG_LEVELS.ERROR then
        print('^1' .. logMessage .. '^7')
    elseif level == LOG_LEVELS.WARN then
        print('^3' .. logMessage .. '^7')
    elseif level == LOG_LEVELS.DEBUG then
        print('^5' .. logMessage .. '^7')
    else
        print('^2' .. logMessage .. '^7')
    end
    
    -- Optional: Database logging (uncomment if you want to store logs in database)
    --[[
    MySQL.insert('INSERT INTO crafting_logs (timestamp, level, event, citizenid, player_name, data, source) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        timestamp,
        level,
        eventType,
        playerInfo.citizenid,
        playerInfo.name,
        json.encode(data),
        src
    })
    --]]
end

-- Specific logging functions for different events
function LogCraftAttempt(src, recipeId, quantity, success)
    Log('craft_attempt', src, {
        recipe = recipeId,
        quantity = quantity,
        success = success,
        recipe_label = Config.Components[recipeId] and Config.Components[recipeId].label or recipeId
    })
end

function LogAssemblyAttempt(src, blueprintId, success)
    Log('assembly_attempt', src, {
        blueprint = blueprintId,
        success = success,
        blueprint_label = Config.Blueprints[blueprintId] and Config.Blueprints[blueprintId].label or blueprintId
    })
end

function LogBenchAccess(src, benchId)
    Log('bench_access', src, {
        bench = benchId,
        bench_label = Config.Benches[benchId] and Config.Benches[benchId].label or benchId
    })
end

function LogSecurityViolation(src, violationType, details)
    Log('security_violation', src, {
        violation_type = violationType,
        details = details
    }, LOG_LEVELS.WARN)
end

function LogError(src, errorType, details)
    Log('error', src, {
        error_type = errorType,
        details = details
    }, LOG_LEVELS.ERROR)
end

-- Export logging functions
exports('Log', Log)
exports('LogCraftAttempt', LogCraftAttempt)
exports('LogAssemblyAttempt', LogAssemblyAttempt)
exports('LogBenchAccess', LogBenchAccess)
exports('LogSecurityViolation', LogSecurityViolation)
exports('LogError', LogError)
