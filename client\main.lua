-- Client-side logic for qb-crafting

local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local isNearBench = false
local currentBench = nil
local isMenuOpen = false
local spawnedProps = {} -- Track spawned bench props

-- Helper function to draw 3D text
function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    if onScreen then
        SetTextScale(0.35, 0.35)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)

        local factor = (string.len(text)) / 370
        DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 0, 0, 0, 75)
    end
end

-- Initialize and spawn existing bench props
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            PlayerData = QBCore.Functions.GetPlayerData()

            -- Spawn props for existing benches if enabled
            if Config.Global.props.spawnProps then
                for benchId, bench in pairs(Config.Benches) do
                    if not spawnedProps[benchId] then
                        TriggerEvent('qb-crafting:spawnBenchProp', benchId, {
                            coords = bench.coords,
                            model = Config.Global.props.benchModel,
                            label = bench.label
                        })
                    end
                end
            end

            break
        end
        Wait(1000)
    end
end)

-- Update player data on job change
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
end)

-- Bench proximity and interaction system
CreateThread(function()
    while true do
        local sleep = 1000
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        
        isNearBench = false
        currentBench = nil
        
        -- Check proximity to all benches
        for benchId, bench in pairs(Config.Benches) do
            local benchCoords = vector3(bench.coords.x, bench.coords.y, bench.coords.z)
            local distance = #(playerCoords - benchCoords)
            
            if distance <= Config.Global.altHoverDistance then
                isNearBench = true
                currentBench = benchId
                sleep = 0
                break
            end
        end
        
        Wait(sleep)
    end
end)

-- Input handling and hover effects
CreateThread(function()
    while true do
        local sleep = 500
        
        if isNearBench and currentBench then
            sleep = 0
            local bench = Config.Benches[currentBench]
            
            -- Show hover hint when holding ALT
            if IsControlPressed(0, Config.Global.hoverKey) then -- ALT
                local benchCoords = vector3(bench.coords.x, bench.coords.y, bench.coords.z)
                
                -- Draw circle marker
                DrawMarker(1, benchCoords.x, benchCoords.y, benchCoords.z - 1.0, 
                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 
                    bench.hoverRadius * 2, bench.hoverRadius * 2, 0.5, 
                    100, 200, 255, 100, false, true, 2, false, nil, nil, false)
                
                -- Show text hint
                SetTextComponentFormat('STRING')
                AddTextComponentString(Config.UI.closeHint)
                DisplayHelpTextFromStringLabel(0, 0, 1, -1)
            end
            
            -- Handle E key press
            if IsControlJustPressed(0, Config.Global.openKey) and not isMenuOpen then -- E
                TriggerServerEvent(Config.Events.server.requestOpenBench, currentBench)
            end
        end
        
        Wait(sleep)
    end
end)

-- Event: Open crafting menu
RegisterNetEvent(Config.Events.client.openMenu, function(benchId, benchInfo)
    if isMenuOpen then
        print('[qb-crafting] Menu already open - force closing first')
        ForceCloseMenu()
        Wait(100) -- Small delay to ensure cleanup
    end

    -- Ensure we start with a clean state
    SetNuiFocus(false, false)
    Wait(50)

    isMenuOpen = true
    SetNuiFocus(true, true)

    -- Send data to NUI
    SendNUIMessage({
        action = 'openMenu',
        benchId = benchId,
        benchInfo = benchInfo,
        config = {
            title = Config.UI.title,
            categories = Config.UI.categories,
            craftBtn = Config.UI.craftBtn,
            assembleBtn = Config.UI.assembleBtn,
            closeBtn = Config.UI.closeBtn
        }
    })

    currentBench = benchId
    print('[qb-crafting] Menu opened successfully')
end)

-- Event: Craft result
RegisterNetEvent(Config.Events.client.craftResult, function(weaponId, success)
    local weapon = Config.Weapons[weaponId]
    local label = weapon and weapon.label or weaponId

    if success then
        QBCore.Functions.Notify(string.format('Successfully crafted %s', label), 'success')
    else
        QBCore.Functions.Notify(string.format('Failed to craft %s', label), 'error')
    end

    -- Update menu if open
    if isMenuOpen and currentBench then
        -- Refresh menu data
        TriggerServerEvent(Config.Events.server.requestOpenBench, currentBench)
    end
end)

-- Enhanced close menu function
local function ForceCloseMenu()
    if isMenuOpen then
        isMenuOpen = false
        SetNuiFocus(false, false)

        -- Multiple attempts to close NUI
        SendNUIMessage({ action = 'forceClose' })
        SendNUIMessage({ action = 'closeMenu' })

        -- Force cursor release
        SetCursorLocation(0.5, 0.5)

        -- Additional safety measures
        CreateThread(function()
            Wait(100)
            SetNuiFocus(false, false) -- Double check
            Wait(100)
            SetNuiFocus(false, false) -- Triple check
        end)

        print('[qb-crafting] Menu force closed')
    end
end

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(data, cb)
    ForceCloseMenu()
    cb('ok')
end)

RegisterNUICallback('craftWeapon', function(data, cb)
    local weaponId = data.weaponId

    if weaponId and Config.Weapons[weaponId] then
        TriggerServerEvent(Config.Events.server.craftWeapon, weaponId)
    end

    cb('ok')
end)

-- Enhanced ESC key handling
CreateThread(function()
    while true do
        if isMenuOpen then
            if IsControlJustPressed(0, 322) then -- ESC
                ForceCloseMenu()
            end

            -- Additional safety check - if NUI focus is lost but menu is still "open"
            if not HasNuiFocus() and isMenuOpen then
                print('[qb-crafting] Detected desync - force closing menu')
                ForceCloseMenu()
            end
        end
        Wait(0)
    end
end)

-- Event: Refresh benches (for dynamic bench spawning)
RegisterNetEvent('qb-crafting:refreshBenches', function()
    -- This will cause the proximity check to pick up new benches
    -- No additional action needed as the main loop will detect new benches automatically
end)

-- Event: Force close menu (emergency close)
RegisterNetEvent('qb-crafting:forceCloseMenu', function()
    ForceCloseMenu()
end)

-- Debug event to check menu state
RegisterNetEvent('qb-crafting:debugMenuState', function()
    print(string.format('[qb-crafting] Menu State Debug:'))
    print(string.format('  isMenuOpen: %s', tostring(isMenuOpen)))
    print(string.format('  HasNuiFocus: %s', tostring(HasNuiFocus())))
    print(string.format('  IsPauseMenuActive: %s', tostring(IsPauseMenuActive())))

    QBCore.Functions.Notify(string.format('Menu Open: %s | NUI Focus: %s',
        tostring(isMenuOpen), tostring(HasNuiFocus())), 'primary', 5000)
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if isMenuOpen then
            print('[qb-crafting] Resource stopping - force closing menu')
            ForceCloseMenu()
        end
    end
end)

-- Periodic state check to prevent stuck UI
CreateThread(function()
    while true do
        Wait(5000) -- Check every 5 seconds

        if isMenuOpen then
            -- Check if NUI focus is lost but menu is still marked as open
            if not HasNuiFocus() then
                print('[qb-crafting] Periodic check: NUI focus lost but menu marked as open - fixing')
                ForceCloseMenu()
            end

            -- Check if player is in a vehicle (shouldn't have crafting menu open)
            if IsPedInAnyVehicle(PlayerPedId(), false) then
                print('[qb-crafting] Periodic check: Player in vehicle - closing menu')
                ForceCloseMenu()
            end
        end
    end
end)

-- Event: Spawn bench prop
RegisterNetEvent('qb-crafting:spawnBenchProp', function(benchId, propData)
    CreateThread(function()
        -- Load the model
        local model = GetHashKey(propData.model)
        RequestModel(model)

        local timeout = 0
        while not HasModelLoaded(model) and timeout < 10000 do
            Wait(100)
            timeout = timeout + 100
        end

        if HasModelLoaded(model) then
            -- Create the prop
            local prop = CreateObject(model, propData.coords.x, propData.coords.y, propData.coords.z, false, false, false)

            if DoesEntityExist(prop) then
                -- Set the heading
                SetEntityHeading(prop, propData.coords.h)

                -- Make it solid and freeze it
                FreezeEntityPosition(prop, true)
                SetEntityInvincible(prop, true)

                -- Store the prop reference
                spawnedProps[benchId] = prop

                -- Add a text label above the bench
                CreateThread(function()
                    while DoesEntityExist(prop) do
                        local propCoords = GetEntityCoords(prop)
                        local playerCoords = GetEntityCoords(PlayerPedId())
                        local distance = #(playerCoords - propCoords)

                        if distance < 10.0 then
                            -- Draw text above the bench
                            local x, y, z = table.unpack(propCoords)
                            DrawText3D(x, y, z + 1.2, propData.label)
                        end

                        Wait(0)
                    end
                end)

                QBCore.Functions.Notify(string.format('Bench "%s" prop spawned', propData.label), 'success')
            else
                QBCore.Functions.Notify('Failed to spawn bench prop', 'error')
            end
        else
            QBCore.Functions.Notify('Failed to load bench model', 'error')
        end

        SetModelAsNoLongerNeeded(model)
    end)
end)

-- Event: Remove bench prop
RegisterNetEvent('qb-crafting:removeBenchProp', function(benchId)
    if spawnedProps[benchId] and DoesEntityExist(spawnedProps[benchId]) then
        DeleteEntity(spawnedProps[benchId])
        spawnedProps[benchId] = nil
        QBCore.Functions.Notify('Bench prop removed', 'primary')
    end
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if isMenuOpen then
            isMenuOpen = false
            SetNuiFocus(false, false)
        end

        -- Clean up spawned props
        for benchId, prop in pairs(spawnedProps) do
            if DoesEntityExist(prop) then
                DeleteEntity(prop)
            end
        end
        spawnedProps = {}
    end
end)
