-- Client-side logic for qb-crafting

local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local isNearBench = false
local currentBench = nil
local isMenuOpen = false

-- Initialize
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            PlayerData = QBCore.Functions.GetPlayerData()
            break
        end
        Wait(1000)
    end
end)

-- Update player data on job change
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
end)

-- Bench proximity and interaction system
CreateThread(function()
    while true do
        local sleep = 1000
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        
        isNearBench = false
        currentBench = nil
        
        -- Check proximity to all benches
        for benchId, bench in pairs(Config.Benches) do
            local benchCoords = vector3(bench.coords.x, bench.coords.y, bench.coords.z)
            local distance = #(playerCoords - benchCoords)
            
            if distance <= Config.Global.altHoverDistance then
                isNearBench = true
                currentBench = benchId
                sleep = 0
                break
            end
        end
        
        Wait(sleep)
    end
end)

-- Input handling and hover effects
CreateThread(function()
    while true do
        local sleep = 500
        
        if isNearBench and currentBench then
            sleep = 0
            local bench = Config.Benches[currentBench]
            
            -- Show hover hint when holding ALT
            if IsControlPressed(0, Config.Global.hoverKey) then -- ALT
                local benchCoords = vector3(bench.coords.x, bench.coords.y, bench.coords.z)
                
                -- Draw circle marker
                DrawMarker(1, benchCoords.x, benchCoords.y, benchCoords.z - 1.0, 
                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 
                    bench.hoverRadius * 2, bench.hoverRadius * 2, 0.5, 
                    100, 200, 255, 100, false, true, 2, false, nil, nil, false)
                
                -- Show text hint
                SetTextComponentFormat('STRING')
                AddTextComponentString(Config.UI.closeHint)
                DisplayHelpTextFromStringLabel(0, 0, 1, -1)
            end
            
            -- Handle E key press
            if IsControlJustPressed(0, Config.Global.openKey) and not isMenuOpen then -- E
                TriggerServerEvent(Config.Events.server.requestOpenBench, currentBench)
            end
        end
        
        Wait(sleep)
    end
end)

-- Event: Open crafting menu
RegisterNetEvent(Config.Events.client.openMenu, function(benchId, benchInfo)
    if isMenuOpen then return end
    
    isMenuOpen = true
    SetNuiFocus(true, true)
    
    -- Send data to NUI
    SendNUIMessage({
        action = 'openMenu',
        benchId = benchId,
        benchInfo = benchInfo,
        config = {
            title = Config.UI.title,
            categories = Config.UI.categories,
            craftBtn = Config.UI.craftBtn,
            assembleBtn = Config.UI.assembleBtn,
            closeBtn = Config.UI.closeBtn
        }
    })
end)

-- Event: Craft result
RegisterNetEvent(Config.Events.client.craftResult, function(recipeId, success, count)
    local component = Config.Components[recipeId]
    local label = component and component.label or recipeId
    
    if success then
        QBCore.Functions.Notify(string.format('Successfully crafted %s (x%d)', label, count or 1), 'success')
    else
        QBCore.Functions.Notify(string.format('Failed to craft %s (x%d)', label, count or 1), 'error')
    end
    
    -- Update menu if open
    if isMenuOpen and currentBench then
        -- Refresh menu data
        TriggerServerEvent(Config.Events.server.requestOpenBench, currentBench)
    end
end)

-- Event: Assembly result
RegisterNetEvent(Config.Events.client.assembleResult, function(blueprintId, success)
    local blueprint = Config.Blueprints[blueprintId]
    local label = blueprint and blueprint.label or blueprintId
    
    if success then
        QBCore.Functions.Notify(string.format('Successfully assembled %s', label), 'success')
    else
        QBCore.Functions.Notify(string.format('Failed to assemble %s', label), 'error')
    end
    
    -- Update menu if open
    if isMenuOpen and currentBench then
        -- Refresh menu data
        TriggerServerEvent(Config.Events.server.requestOpenBench, currentBench)
    end
end)

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(data, cb)
    isMenuOpen = false
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('craftComponent', function(data, cb)
    local recipeId = data.recipeId
    local quantity = data.quantity or 1
    
    if recipeId and Config.ComponentRecipes[recipeId] then
        TriggerServerEvent(Config.Events.server.startCraftComponent, recipeId, quantity)
    end
    
    cb('ok')
end)

RegisterNUICallback('assembleBlueprint', function(data, cb)
    local blueprintId = data.blueprintId
    
    if blueprintId and Config.Blueprints[blueprintId] then
        TriggerServerEvent(Config.Events.server.assembleBlueprint, blueprintId)
    end
    
    cb('ok')
end)

-- Close menu on ESC
CreateThread(function()
    while true do
        if isMenuOpen then
            if IsControlJustPressed(0, 322) then -- ESC
                SendNUIMessage({ action = 'closeMenu' })
                isMenuOpen = false
                SetNuiFocus(false, false)
            end
        end
        Wait(0)
    end
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if isMenuOpen then
            isMenuOpen = false
            SetNuiFocus(false, false)
        end
    end
end)
