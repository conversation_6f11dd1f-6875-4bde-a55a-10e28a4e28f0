-- Server-side validation and anti-exploit helpers

local QBCore = exports['qb-core']:GetCoreObject()
local throttleData = {}

-- Throttling system
function ValidateThrottle(src, action)
    local now = GetGameTimer()
    local playerId = tostring(src)
    
    if not throttleData[playerId] then
        throttleData[playerId] = {}
    end
    
    local lastAction = throttleData[playerId][action] or 0
    local throttleTime = Config.Global.throttle[action] or 1000
    
    if now - lastAction < throttleTime then
        return false
    end
    
    throttleData[playerId][action] = now
    return true
end

-- Distance validation
function IsNearBench(src, bench)
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return false end
    
    local playerCoords = GetEntityCoords(GetPlayerPed(src))
    local benchCoords = vector3(bench.coords.x, bench.coords.y, bench.coords.z)
    local distance = #(playerCoords - benchCoords)
    
    return distance <= Config.Global.distanceTolerance
end

-- Permission validation
function HasPermission(src, bench)
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return false end
    
    -- Job check
    if Config.Global.requireJob and Config.Global.requiredJob then
        if Player.PlayerData.job.name ~= Config.Global.requiredJob then
            return false
        end
    end
    
    -- ACE permission check
    if Config.Global.requireAce and Config.Global.aceName then
        if not IsPlayerAceAllowed(src, Config.Global.aceName) then
            return false
        end
    end
    
    -- Bench level check
    local playerLevel = Player.PlayerData.metadata.craftingLevel or 1
    local requiredLevel = bench.level or 1
    if playerLevel < requiredLevel then
        return false
    end
    
    return true
end

-- Inventory helpers
function HasItem(src, itemName, amount)
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return false end
    
    local item = Player.Functions.GetItemByName(itemName)
    return item and item.amount >= (amount or 1)
end

function AddItem(src, itemName, amount, metadata)
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return false end
    
    return Player.Functions.AddItem(itemName, amount or 1, false, metadata)
end

function RemoveItem(src, itemName, amount)
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return false end
    
    return Player.Functions.RemoveItem(itemName, amount or 1)
end

-- Atomic inventory operations
function HasAndRemoveInputs(src, inputs, multiplier)
    multiplier = multiplier or 1
    
    -- First check if player has all required items
    for itemName, amount in pairs(inputs) do
        if not HasItem(src, itemName, amount * multiplier) then
            return false
        end
    end
    
    -- If we have everything, remove all items atomically
    for itemName, amount in pairs(inputs) do
        if not RemoveItem(src, itemName, amount * multiplier) then
            -- If any removal fails, we have a problem
            -- In a real implementation, you'd want to rollback previous removals
            return false
        end
    end
    
    return true
end

function RemoveItems(src, itemList)
    -- Check if player has all items first
    for _, itemName in ipairs(itemList) do
        if not HasItem(src, itemName, 1) then
            return false
        end
    end
    
    -- Remove all items atomically
    for _, itemName in ipairs(itemList) do
        if not RemoveItem(src, itemName, 1) then
            return false
        end
    end
    
    return true
end

function HasItems(src, itemList)
    for _, itemName in ipairs(itemList) do
        if not HasItem(src, itemName, 1) then
            return false
        end
    end
    return true
end

-- Partial refund on craft failure
function PartialRefund(src, inputs, refundRate)
    for itemName, amount in pairs(inputs) do
        local refundAmount = math.floor(amount * refundRate)
        if refundAmount > 0 then
            AddItem(src, itemName, refundAmount)
        end
    end
end

-- Notification helper
function Notify(src, message, type)
    TriggerClientEvent('QBCore:Notify', src, message, type or 'primary')
end

-- Build bench info for client
function BuildBenchInfo(src, bench)
    local info = {
        benchId = bench.id,
        label = bench.label,
        level = bench.level or 1,
        supported = {},
        components = {}
    }
    
    -- Add supported blueprints with component status
    for _, bpId in ipairs(bench.supported) do
        local blueprint = Config.Blueprints[bpId]
        if blueprint then
            local canAssemble = HasItems(src, blueprint.requiredComponents)
            local componentStatus = {}
            
            for _, compId in ipairs(blueprint.requiredComponents) do
                local hasComp = HasItem(src, compId, 1)
                table.insert(componentStatus, {
                    id = compId,
                    label = Config.Components[compId] and Config.Components[compId].label or compId,
                    has = hasComp
                })
            end
            
            table.insert(info.supported, {
                id = bpId,
                label = blueprint.label,
                twoHanded = blueprint.twoHanded,
                canAssemble = canAssemble,
                components = componentStatus,
                assembleTime = blueprint.assembleTime
            })
        end
    end
    
    -- Add component recipes
    for recipeId, recipe in pairs(Config.ComponentRecipes) do
        local canCraft = true
        local inputStatus = {}
        
        for itemName, amount in pairs(recipe.inputs) do
            local hasAmount = 0
            if HasItem(src, itemName, amount) then
                local Player = QBCore.Functions.GetPlayer(src)
                local item = Player.Functions.GetItemByName(itemName)
                hasAmount = item and item.amount or 0
            end
            
            table.insert(inputStatus, {
                id = itemName,
                label = Config.Resources[itemName] and Config.Resources[itemName].label or itemName,
                needed = amount,
                has = hasAmount
            })
            
            if hasAmount < amount then
                canCraft = false
            end
        end
        
        table.insert(info.components, {
            id = recipeId,
            label = Config.Components[recipeId] and Config.Components[recipeId].label or recipeId,
            canCraft = canCraft,
            inputs = inputStatus,
            craftTime = recipe.craftTime,
            successChance = recipe.successChance
        })
    end
    
    return info
end
