-- Server-side validation and anti-exploit helpers

local QBCore = exports['qb-core']:GetCoreObject()
local throttleData = {}

-- ox_inventory integration
local function GetInventory()
    if Config.Global.inventory.useOxInventory then
        return exports.ox_inventory
    else
        return nil -- fallback to QB inventory
    end
end

-- Throttling system
function ValidateThrottle(src, action)
    local now = GetGameTimer()
    local playerId = tostring(src)
    
    if not throttleData[playerId] then
        throttleData[playerId] = {}
    end
    
    local lastAction = throttleData[playerId][action] or 0
    local throttleTime = Config.Global.throttle[action] or 1000
    
    if now - lastAction < throttleTime then
        return false
    end
    
    throttleData[playerId][action] = now
    return true
end

-- Distance validation
function IsNearBench(src, bench)
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return false end
    
    local playerCoords = GetEntityCoords(GetPlayerPed(src))
    local benchCoords = vector3(bench.coords.x, bench.coords.y, bench.coords.z)
    local distance = #(playerCoords - benchCoords)
    
    return distance <= Config.Global.distanceTolerance
end

-- Permission validation
function HasPermission(src, bench)
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return false end
    
    -- Job check
    if Config.Global.requireJob and Config.Global.requiredJob then
        if Player.PlayerData.job.name ~= Config.Global.requiredJob then
            return false
        end
    end
    
    -- ACE permission check
    if Config.Global.requireAce and Config.Global.aceName then
        if not IsPlayerAceAllowed(src, Config.Global.aceName) then
            return false
        end
    end
    
    -- Bench level check
    local playerLevel = Player.PlayerData.metadata.craftingLevel or 1
    local requiredLevel = bench.level or 1
    if playerLevel < requiredLevel then
        return false
    end
    
    return true
end

-- Inventory helpers with ox_inventory support
function HasItem(src, itemName, amount)
    amount = amount or 1

    if Config.Global.inventory.useOxInventory then
        local count = exports.ox_inventory:GetItemCount(src, itemName)
        return count >= amount
    else
        local Player = QBCore.Functions.GetPlayer(src)
        if not Player then return false end

        local item = Player.Functions.GetItemByName(itemName)
        return item and item.amount >= amount
    end
end

function GetItemCount(src, itemName)
    if Config.Global.inventory.useOxInventory then
        return exports.ox_inventory:GetItemCount(src, itemName)
    else
        local Player = QBCore.Functions.GetPlayer(src)
        if not Player then return 0 end

        local item = Player.Functions.GetItemByName(itemName)
        return item and item.amount or 0
    end
end

function AddItem(src, itemName, amount, metadata)
    amount = amount or 1

    if Config.Global.inventory.useOxInventory then
        return exports.ox_inventory:AddItem(src, itemName, amount, metadata)
    else
        local Player = QBCore.Functions.GetPlayer(src)
        if not Player then return false end

        return Player.Functions.AddItem(itemName, amount, false, metadata)
    end
end

function RemoveItem(src, itemName, amount)
    amount = amount or 1

    if Config.Global.inventory.useOxInventory then
        return exports.ox_inventory:RemoveItem(src, itemName, amount)
    else
        local Player = QBCore.Functions.GetPlayer(src)
        if not Player then return false end

        return Player.Functions.RemoveItem(itemName, amount)
    end
end

-- Simplified atomic operations for direct iron crafting
function HasAndRemoveIronIngots(src, amount)
    -- Check if player has enough iron ingots
    if not HasItem(src, 'ls_iron_ingot', amount) then
        return false, GetItemCount(src, 'ls_iron_ingot')
    end

    -- Remove iron ingots atomically
    if not RemoveItem(src, 'ls_iron_ingot', amount) then
        return false, GetItemCount(src, 'ls_iron_ingot')
    end

    return true, amount
end

-- Partial refund on craft failure
function RefundIronIngots(src, amount, refundRate)
    local refundAmount = math.floor(amount * refundRate)
    if refundAmount > 0 then
        AddItem(src, 'ls_iron_ingot', refundAmount)
    end
    return refundAmount
end

-- Notification helper
function Notify(src, message, type)
    TriggerClientEvent('QBCore:Notify', src, message, type or 'primary')
end

-- Build bench info for client (simplified for direct weapon crafting)
function BuildBenchInfo(src, bench)
    local info = {
        benchId = bench.id,
        label = bench.label,
        level = bench.level or 1,
        weapons = {}
    }

    local playerIronCount = GetItemCount(src, 'ls_iron_ingot')

    -- Add supported weapons with iron requirements
    for _, weaponId in ipairs(bench.supported) do
        local weapon = Config.Weapons[weaponId]
        if weapon then
            local canCraft = playerIronCount >= weapon.ironRequired

            table.insert(info.weapons, {
                id = weaponId,
                label = weapon.label,
                twoHanded = weapon.twoHanded,
                ironRequired = weapon.ironRequired,
                playerHas = playerIronCount,
                canCraft = canCraft,
                craftTime = weapon.craftTime,
                successChance = weapon.successChance,
                requiredBenchLevel = weapon.requiredBenchLevel or 1
            })
        end
    end

    return info
end
