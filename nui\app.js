// NUI JavaScript for qb-crafting

let currentBenchData = null;
let currentCategory = 'guns';

// DOM Elements
const app = document.getElementById('app');
const closeBtn = document.getElementById('close-btn');
const categoryBtns = document.querySelectorAll('.category-btn');
const categoryContents = document.querySelectorAll('.category-content');
const modal = document.getElementById('item-modal');
const modalClose = document.getElementById('modal-close');
const modalTitle = document.getElementById('modal-title');
const modalRequirements = document.getElementById('modal-requirements');
const modalActionBtn = document.getElementById('modal-action-btn');
const quantityInput = document.getElementById('quantity');

// Event Listeners
closeBtn.addEventListener('click', closeMenu);
modalClose.addEventListener('click', closeModal);

// Category switching
categoryBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        const category = btn.dataset.category;
        switchCategory(category);
    });
});

// Modal action button
modalActionBtn.addEventListener('click', handleModalAction);

// Close on escape
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        if (!modal.classList.contains('hidden')) {
            closeModal();
        } else {
            closeMenu();
        }
    }
});

// NUI Message Handler
window.addEventListener('message', (event) => {
    const data = event.data;
    
    switch (data.action) {
        case 'openMenu':
            openMenu(data.benchId, data.benchInfo, data.config);
            break;
        case 'closeMenu':
            closeMenu();
            break;
    }
});

// Functions
function openMenu(benchId, benchInfo, config) {
    currentBenchData = benchInfo;
    
    // Update title
    document.getElementById('menu-title').textContent = config.title;
    
    // Populate categories
    populateGuns(benchInfo.supported);
    populateComponents(benchInfo.components);
    
    // Show menu
    app.classList.remove('hidden');
}

function closeMenu() {
    app.classList.add('hidden');
    closeModal();
    
    // Send close callback to client
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    });
}

function closeModal() {
    modal.classList.add('hidden');
}

function switchCategory(category) {
    currentCategory = category;
    
    // Update active category button
    categoryBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.category === category);
    });
    
    // Update active content
    categoryContents.forEach(content => {
        content.classList.toggle('active', content.id === `${category}-content`);
    });
}

function populateGuns(guns) {
    const gunsGrid = document.getElementById('guns-grid');
    gunsGrid.innerHTML = '';
    
    if (!guns || guns.length === 0) {
        gunsGrid.innerHTML = '<div class="placeholder"><p>No guns available at this bench.</p></div>';
        return;
    }
    
    guns.forEach(gun => {
        const card = createGunCard(gun);
        gunsGrid.appendChild(card);
    });
}

function populateComponents(components) {
    const componentsGrid = document.getElementById('components-grid');
    componentsGrid.innerHTML = '';
    
    if (!components || components.length === 0) {
        componentsGrid.innerHTML = '<div class="placeholder"><p>No components available.</p></div>';
        return;
    }
    
    components.forEach(component => {
        const card = createComponentCard(component);
        componentsGrid.appendChild(card);
    });
}

function createGunCard(gun) {
    const card = document.createElement('div');
    card.className = `item-card ${gun.canAssemble ? '' : 'disabled'}`;
    
    const typeLabel = gun.twoHanded ? '2H' : '1H';
    
    let requirementsHtml = '';
    gun.components.forEach(comp => {
        const statusClass = comp.has ? 'has' : 'missing';
        const statusText = comp.has ? '✓' : '✗';
        requirementsHtml += `
            <div class="requirement">
                <span class="requirement-name">${comp.label}</span>
                <span class="requirement-amount ${statusClass}">${statusText}</span>
            </div>
        `;
    });
    
    card.innerHTML = `
        <div class="item-header">
            <span class="item-title">${gun.label}</span>
            <span class="item-type">${typeLabel}</span>
        </div>
        <div class="item-requirements">
            ${requirementsHtml}
        </div>
    `;
    
    if (gun.canAssemble) {
        card.addEventListener('click', () => {
            showGunModal(gun);
        });
    }
    
    return card;
}

function createComponentCard(component) {
    const card = document.createElement('div');
    card.className = `item-card ${component.canCraft ? '' : 'disabled'}`;
    
    let requirementsHtml = '';
    component.inputs.forEach(input => {
        const statusClass = input.has >= input.needed ? 'has' : 'missing';
        requirementsHtml += `
            <div class="requirement">
                <span class="requirement-name">${input.label}</span>
                <span class="requirement-amount ${statusClass}">${input.has}/${input.needed}</span>
            </div>
        `;
    });
    
    card.innerHTML = `
        <div class="item-header">
            <span class="item-title">${component.label}</span>
            <span class="item-type">Component</span>
        </div>
        <div class="item-requirements">
            ${requirementsHtml}
        </div>
    `;
    
    if (component.canCraft) {
        card.addEventListener('click', () => {
            showComponentModal(component);
        });
    }
    
    return card;
}

function showGunModal(gun) {
    modalTitle.textContent = gun.label;
    
    let requirementsHtml = '';
    gun.components.forEach(comp => {
        const statusClass = comp.has ? 'has' : 'missing';
        const statusText = comp.has ? '✓ Available' : '✗ Missing';
        requirementsHtml += `
            <div class="requirement">
                <span class="requirement-name">${comp.label}</span>
                <span class="requirement-amount ${statusClass}">${statusText}</span>
            </div>
        `;
    });
    
    modalRequirements.innerHTML = requirementsHtml;
    
    // Hide quantity selector for assembly
    document.querySelector('.quantity-selector').style.display = 'none';
    
    modalActionBtn.textContent = 'Assemble';
    modalActionBtn.disabled = !gun.canAssemble;
    modalActionBtn.onclick = () => assembleGun(gun.id);
    
    modal.classList.remove('hidden');
}

function showComponentModal(component) {
    modalTitle.textContent = component.label;
    
    let requirementsHtml = '';
    component.inputs.forEach(input => {
        const statusClass = input.has >= input.needed ? 'has' : 'missing';
        requirementsHtml += `
            <div class="requirement">
                <span class="requirement-name">${input.label}</span>
                <span class="requirement-amount ${statusClass}">${input.has}/${input.needed}</span>
            </div>
        `;
    });
    
    modalRequirements.innerHTML = requirementsHtml;
    
    // Show quantity selector for crafting
    document.querySelector('.quantity-selector').style.display = 'flex';
    quantityInput.value = 1;
    
    modalActionBtn.textContent = 'Craft';
    modalActionBtn.disabled = !component.canCraft;
    modalActionBtn.onclick = () => craftComponent(component.id);
    
    modal.classList.remove('hidden');
}

function assembleGun(gunId) {
    fetch(`https://${GetParentResourceName()}/assembleBlueprint`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ blueprintId: gunId })
    });
    
    closeModal();
}

function craftComponent(componentId) {
    const quantity = parseInt(quantityInput.value) || 1;
    
    fetch(`https://${GetParentResourceName()}/craftComponent`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            recipeId: componentId,
            quantity: quantity
        })
    });
    
    closeModal();
}

function handleModalAction() {
    // This function is overridden by the specific modal show functions
}

// Utility function to get resource name
function GetParentResourceName() {
    return window.location.hostname;
}
