// NUI JavaScript for qb-crafting

let currentBenchData = null;
let currentCategory = 'guns';

// DOM Elements
const app = document.getElementById('app');
const closeBtn = document.getElementById('close-btn');
const categoryBtns = document.querySelectorAll('.category-btn');
const categoryContents = document.querySelectorAll('.category-content');
const modal = document.getElementById('item-modal');
const modalClose = document.getElementById('modal-close');
const modalTitle = document.getElementById('modal-title');
const modalRequirements = document.getElementById('modal-requirements');
const modalActionBtn = document.getElementById('modal-action-btn');

// Event Listeners
closeBtn.addEventListener('click', closeMenu);
modalClose.addEventListener('click', closeModal);

// Category switching
categoryBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        const category = btn.dataset.category;
        switchCategory(category);
    });
});

// Modal action button
modalActionBtn.addEventListener('click', handleModalAction);

// Enhanced escape key handling
function handleEscapeKey(e) {
    if (e.key === 'Escape') {
        console.log('[qb-crafting] Escape key pressed');
        if (!modal.classList.contains('hidden')) {
            closeModal();
        } else {
            closeMenu();
        }
    }
}

document.addEventListener('keydown', handleEscapeKey);

// Handle window focus/blur events
window.addEventListener('blur', function() {
    console.log('[qb-crafting] Window lost focus');
});

window.addEventListener('focus', function() {
    console.log('[qb-crafting] Window gained focus');
});

// Handle visibility change
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('[qb-crafting] Page became hidden');
    } else {
        console.log('[qb-crafting] Page became visible');
    }
});

// NUI Message Handler
window.addEventListener('message', (event) => {
    const data = event.data;
    
    switch (data.action) {
        case 'openMenu':
            openMenu(data.benchId, data.benchInfo, data.config);
            break;
        case 'closeMenu':
            closeMenu();
            break;
        case 'forceClose':
            console.log('[qb-crafting] Force closing UI...');
            app.classList.add('hidden');
            app.style.display = 'none';
            modal.classList.add('hidden');
            modal.style.display = 'none';

            // Remove all event listeners
            document.removeEventListener('keydown', handleEscapeKey);

            // Clear any timers or intervals
            clearTimeout(window.craftingTimeout);
            break;
    }
});

// Functions
function openMenu(benchId, benchInfo, config) {
    console.log('[qb-crafting] Opening menu...');
    currentBenchData = benchInfo;

    // Ensure menu is properly reset
    app.style.display = '';
    modal.style.display = '';

    // Update title
    document.getElementById('menu-title').textContent = config.title;

    // Populate weapons
    populateWeapons(benchInfo.weapons);

    // Add escape key listener
    document.addEventListener('keydown', handleEscapeKey);

    // Show menu
    app.classList.remove('hidden');

    console.log('[qb-crafting] Menu opened successfully');
}

function closeMenu() {
    console.log('[qb-crafting] Closing menu...');

    // Force hide all elements
    app.classList.add('hidden');
    closeModal();

    // Remove any event listeners that might interfere
    document.removeEventListener('keydown', handleEscapeKey);

    // Send close callback to client
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    }).then(() => {
        console.log('[qb-crafting] Close callback sent successfully');
    }).catch(err => {
        console.log('[qb-crafting] Error closing menu:', err);
        // Force close anyway
        app.style.display = 'none';
    });
}

function closeModal() {
    modal.classList.add('hidden');
    modal.style.display = 'none'; // Force hide
}

function switchCategory(category) {
    currentCategory = category;
    
    // Update active category button
    categoryBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.category === category);
    });
    
    // Update active content
    categoryContents.forEach(content => {
        content.classList.toggle('active', content.id === `${category}-content`);
    });
}

function populateWeapons(weapons) {
    const gunsGrid = document.getElementById('guns-grid');
    gunsGrid.innerHTML = '';

    if (!weapons || weapons.length === 0) {
        gunsGrid.innerHTML = '<div class="placeholder"><p>No weapons available at this bench.</p></div>';
        return;
    }

    weapons.forEach(weapon => {
        const card = createWeaponCard(weapon);
        gunsGrid.appendChild(card);
    });
}

function createWeaponCard(weapon) {
    const card = document.createElement('div');
    card.className = `item-card ${weapon.canCraft ? '' : 'disabled'}`;

    const typeLabel = weapon.twoHanded ? '2H' : '1H';
    const statusClass = weapon.canCraft ? 'has' : 'missing';

    card.innerHTML = `
        <div class="item-header">
            <span class="item-title">${weapon.label}</span>
            <span class="item-type">${typeLabel}</span>
        </div>
        <div class="item-requirements">
            <div class="requirement">
                <span class="requirement-name">Iron Ingots Required</span>
                <span class="requirement-amount ${statusClass}">${weapon.playerHas}/${weapon.ironRequired}</span>
            </div>
            <div class="requirement">
                <span class="requirement-name">Craft Time</span>
                <span class="requirement-amount">${weapon.craftTime}s</span>
            </div>
            <div class="requirement">
                <span class="requirement-name">Success Rate</span>
                <span class="requirement-amount">${Math.round(weapon.successChance * 100)}%</span>
            </div>
        </div>
    `;

    if (weapon.canCraft) {
        card.addEventListener('click', () => {
            showWeaponModal(weapon);
        });
    }

    return card;
}

function showWeaponModal(weapon) {
    modalTitle.textContent = weapon.label;

    const statusClass = weapon.canCraft ? 'has' : 'missing';
    const statusText = weapon.canCraft ? 'Available' : 'Insufficient Materials';

    let requirementsHtml = `
        <div class="requirement">
            <span class="requirement-name">Iron Ingots Required</span>
            <span class="requirement-amount ${statusClass}">${weapon.playerHas}/${weapon.ironRequired}</span>
        </div>
        <div class="requirement">
            <span class="requirement-name">Craft Time</span>
            <span class="requirement-amount">${weapon.craftTime} seconds</span>
        </div>
        <div class="requirement">
            <span class="requirement-name">Success Rate</span>
            <span class="requirement-amount">${Math.round(weapon.successChance * 100)}%</span>
        </div>
        <div class="requirement">
            <span class="requirement-name">Status</span>
            <span class="requirement-amount ${statusClass}">${statusText}</span>
        </div>
    `;

    modalRequirements.innerHTML = requirementsHtml;

    modalActionBtn.textContent = 'Craft Weapon';
    modalActionBtn.disabled = !weapon.canCraft;
    modalActionBtn.onclick = () => craftWeapon(weapon.id);

    modal.classList.remove('hidden');
}

function craftWeapon(weaponId) {
    fetch(`https://${GetParentResourceName()}/craftWeapon`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ weaponId: weaponId })
    });

    closeModal();
}

function handleModalAction() {
    // This function is overridden by the specific modal show functions
}

// Utility function to get resource name
function GetParentResourceName() {
    return window.location.hostname;
}
