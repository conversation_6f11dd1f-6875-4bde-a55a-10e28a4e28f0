/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: #ffffff;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

/* Main container */
#app {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.crafting-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid #444;
    border-radius: 12px;
    width: 90%;
    max-width: 1000px;
    height: 80%;
    max-height: 700px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header */
.header {
    background: linear-gradient(90deg, #333 0%, #444 100%);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #555;
}

.header h1 {
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;
}

.close-button {
    background: #ff4444;
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    transition: background 0.3s;
}

.close-button:hover {
    background: #ff6666;
}

/* Content area */
.content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Categories */
.categories {
    display: flex;
    background: #2a2a2a;
    border-bottom: 2px solid #555;
}

.category-btn {
    flex: 1;
    padding: 15px;
    background: transparent;
    border: none;
    color: #ccc;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
    border-right: 1px solid #555;
}

.category-btn:last-child {
    border-right: none;
}

.category-btn:hover {
    background: #3a3a3a;
    color: #fff;
}

.category-btn.active {
    background: #4a90e2;
    color: #fff;
}

/* Items container */
.items-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.category-content {
    display: none;
}

.category-content.active {
    display: block;
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
}

/* Item cards */
.item-card {
    background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
    border: 2px solid #555;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
}

.item-card:hover {
    border-color: #4a90e2;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

.item-card.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.item-card.disabled:hover {
    transform: none;
    border-color: #555;
    box-shadow: none;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.item-title {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
}

.item-type {
    background: #4a90e2;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.item-requirements {
    margin-top: 10px;
}

.requirement {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #444;
}

.requirement:last-child {
    border-bottom: none;
}

.requirement-name {
    color: #ccc;
    font-size: 14px;
}

.requirement-amount {
    font-size: 14px;
    font-weight: 600;
}

.requirement-amount.has {
    color: #4caf50;
}

.requirement-amount.missing {
    color: #f44336;
}

/* Placeholder */
.placeholder {
    text-align: center;
    padding: 50px;
    color: #888;
    font-size: 18px;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid #444;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    background: linear-gradient(90deg, #333 0%, #444 100%);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #555;
}

.modal-header h2 {
    color: #ffffff;
    font-size: 20px;
}

.modal-body {
    padding: 20px;
}

.requirements {
    margin-bottom: 20px;
}

.modal-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-selector label {
    color: #ccc;
    font-size: 14px;
}

.quantity-selector input {
    background: #333;
    border: 1px solid #555;
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    width: 80px;
    text-align: center;
}

.action-button {
    background: #4a90e2;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s;
    flex: 1;
    max-width: 150px;
}

.action-button:hover {
    background: #357abd;
}

.action-button:disabled {
    background: #666;
    cursor: not-allowed;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2a2a2a;
}

::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #666;
}
