-- Shared contracts and validation schemas for qb-crafting

-- Sample menu structure for reference
SampleMenu = {
    title = Config.UI.title,
    sections = {
        {
            id = 'guns', label = 'Guns',
            items = {
                -- runtime-populated from bench.supported
                -- { id='advanced_rifle', label='Advanced Rifle', twoHanded=true, canAssemble=true, need={'comp_body:0/1', ...}, bench='bench_rifle', time=20 }
            }
        },
        {
            id = 'components', label = 'Components',
            items = {
                -- from Config.ComponentRecipes
                -- { id='comp_barrel', label='Barrel Component', inputs={'ls_iron_ingot:2','ls_copper_ingot:1'}, time=18, canCraft=true }
            }
        },
        { id = 'attachments', label = 'Attachments', items = {} }, -- stub for future
        { id = 'ammo', label = 'Ammo', items = {} }                -- stub for future
    }
}

-- Validation helpers
function ValidateCoords(coords)
    return coords and coords.x and coords.y and coords.z and coords.h
end

function ValidateRecipe(recipe)
    return recipe and recipe.inputs and recipe.craftTime and recipe.successChance
end

function ValidateBlueprint(blueprint)
    return blueprint and blueprint.label and blueprint.finalItem and 
           blueprint.requiredComponents and blueprint.assembleTime and blueprint.bench
end

function ValidateBench(bench)
    return bench and bench.label and ValidateCoords(bench.coords) and 
           bench.supported and bench.hoverRadius
end

-- Schema validation for config integrity
function ValidateConfig()
    local errors = {}
    
    -- Validate benches
    for benchId, bench in pairs(Config.Benches or {}) do
        if not ValidateBench(bench) then
            table.insert(errors, "Invalid bench: " .. benchId)
        end
    end
    
    -- Validate blueprints
    for bpId, blueprint in pairs(Config.Blueprints or {}) do
        if not ValidateBlueprint(blueprint) then
            table.insert(errors, "Invalid blueprint: " .. bpId)
        end
        
        -- Check if bench exists
        if blueprint.bench and not Config.Benches[blueprint.bench] then
            table.insert(errors, "Blueprint " .. bpId .. " references non-existent bench: " .. blueprint.bench)
        end
    end
    
    -- Validate component recipes
    for recipeId, recipe in pairs(Config.ComponentRecipes or {}) do
        if not ValidateRecipe(recipe) then
            table.insert(errors, "Invalid recipe: " .. recipeId)
        end
    end
    
    return #errors == 0, errors
end

-- Export validation function
if IsDuplicityVersion() then
    -- Server side
    exports('ValidateConfig', ValidateConfig)
else
    -- Client side
    exports('ValidateConfig', ValidateConfig)
end
