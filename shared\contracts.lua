-- Shared contracts and validation schemas for qb-crafting

-- Sample menu structure for reference
SampleMenu = {
    title = Config.UI.title,
    sections = {
        {
            id = 'guns', label = 'Weapons',
            items = {
                -- runtime-populated from bench.supported
                -- { id='weapon_ap_pistol', label='AP Pistol', twoHanded=false, ironRequired=8, playerHas=12, canCraft=true, craftTime=25 }
            }
        }
    }
}

-- Validation helpers
function ValidateCoords(coords)
    return coords and coords.x and coords.y and coords.z and coords.h
end

function ValidateWeapon(weapon)
    return weapon and weapon.label and weapon.finalItem and
           weapon.ironRequired and weapon.craftTime and weapon.bench and weapon.successChance
end

function ValidateBench(bench)
    return bench and bench.label and ValidateCoords(bench.coords) and 
           bench.supported and bench.hoverRadius
end

-- Schema validation for config integrity
function ValidateConfig()
    local errors = {}
    
    -- Validate benches
    for benchId, bench in pairs(Config.Benches or {}) do
        if not ValidateBench(bench) then
            table.insert(errors, "Invalid bench: " .. benchId)
        end
    end
    
    -- Validate weapons
    for weaponId, weapon in pairs(Config.Weapons or {}) do
        if not ValidateWeapon(weapon) then
            table.insert(errors, "Invalid weapon: " .. weaponId)
        end

        -- Check if bench exists
        if weapon.bench and not Config.Benches[weapon.bench] then
            table.insert(errors, "Weapon " .. weaponId .. " references non-existent bench: " .. weapon.bench)
        end
    end
    
    return #errors == 0, errors
end

-- Export validation function
if IsDuplicityVersion() then
    -- Server side
    exports('ValidateConfig', ValidateConfig)
else
    -- Client side
    exports('ValidateConfig', ValidateConfig)
end
