-- Main server-side logic for qb-crafting

local QBCore = exports['qb-core']:GetCoreObject()

-- Validate config on resource start
CreateThread(function()
    local isValid, errors = ValidateConfig()
    if not isValid then
        print('^1[qb-crafting] Configuration validation failed:^7')
        for _, error in ipairs(errors) do
            print('^1  - ' .. error .. '^7')
        end
        print('^1[qb-crafting] Please fix configuration errors before using the resource.^7')
    else
        print('^2[qb-crafting] Configuration validated successfully.^7')
    end
end)

-- Event: Request to open bench
RegisterNetEvent(Config.Events.server.requestOpenBench, function(benchId)
    local src = source
    
    -- Validate throttle
    if not ValidateThrottle(src, 'openBench') then
        LogSecurityViolation(src, 'throttle_violation', { action = 'openBench', bench = benchId })
        return
    end
    
    -- Validate bench exists
    local bench = Config.Benches[benchId]
    if not bench then
        LogSecurityViolation(src, 'invalid_bench', { bench = benchId })
        return
    end
    
    -- Validate distance
    if not IsNearBench(src, bench) then
        Notify(src, Config.UI.notNearBench, 'error')
        LogSecurityViolation(src, 'distance_violation', { bench = benchId })
        return
    end
    
    -- Validate permissions
    if not HasPermission(src, bench) then
        Notify(src, Config.UI.benchLocked, 'error')
        LogSecurityViolation(src, 'permission_violation', { bench = benchId })
        return
    end
    
    -- Log successful access
    LogBenchAccess(src, benchId)
    
    -- Build bench info and send to client
    local benchInfo = BuildBenchInfo(src, bench)
    benchInfo.benchId = benchId
    TriggerClientEvent(Config.Events.client.openMenu, src, benchId, benchInfo)
end)

-- Event: Craft weapon directly
RegisterNetEvent(Config.Events.server.craftWeapon, function(weaponId)
    local src = source

    -- Validate throttle
    if not ValidateThrottle(src, 'startCraft') then
        LogSecurityViolation(src, 'throttle_violation', { action = 'craftWeapon', weapon = weaponId })
        return
    end

    -- Validate weapon exists
    local weapon = Config.Weapons[weaponId]
    if not weapon then
        LogSecurityViolation(src, 'invalid_weapon', { weapon = weaponId })
        return
    end

    -- Validate bench exists and player has access
    local bench = Config.Benches[weapon.bench]
    if not bench then
        LogError(src, 'invalid_bench_reference', { weapon = weaponId, bench = weapon.bench })
        return
    end

    -- Validate distance
    if not IsNearBench(src, bench) then
        Notify(src, Config.UI.notNearBench, 'error')
        LogSecurityViolation(src, 'distance_violation', { action = 'craftWeapon', bench = weapon.bench })
        return
    end

    -- Validate permissions and bench level
    if not HasPermission(src, bench) then
        Notify(src, Config.UI.benchLocked, 'error')
        LogSecurityViolation(src, 'permission_violation', { action = 'craftWeapon', bench = weapon.bench })
        return
    end

    local requiredLevel = weapon.requiredBenchLevel or 1
    if (bench.level or 1) < requiredLevel then
        Notify(src, Config.UI.benchLocked, 'error')
        LogSecurityViolation(src, 'bench_level_violation', { weapon = weaponId, required = requiredLevel, actual = bench.level })
        return
    end

    -- Check and remove iron ingots
    local success, currentAmount = HasAndRemoveIronIngots(src, weapon.ironRequired)
    if not success then
        local message = string.format(Config.UI.insufficientMaterials, weapon.ironRequired, currentAmount)
        Notify(src, message, 'error')
        return
    end

    -- Notify client crafting started
    Notify(src, Config.UI.crafting, 'primary')

    -- Schedule crafting completion
    SetTimeout(weapon.craftTime * 1000, function()
        if math.random() <= weapon.successChance then
            -- Success - add weapon
            if AddItem(src, weapon.finalItem, 1) then
                Notify(src, string.format('Successfully crafted %s', weapon.label), 'success')
                LogCraftAttempt(src, weaponId, 1, true)
                TriggerClientEvent(Config.Events.client.craftResult, src, weaponId, true)
            else
                -- Failed to add weapon - return iron ingots
                AddItem(src, 'ls_iron_ingot', weapon.ironRequired)
                Notify(src, 'Crafting failed - inventory full', 'error')
                LogError(src, 'craft_inventory_full', { weapon = weaponId })
                TriggerClientEvent(Config.Events.client.craftResult, src, weaponId, false)
            end
        else
            -- Failure - give partial refund
            local refunded = RefundIronIngots(src, weapon.ironRequired, Config.Global.craftFailRefund)
            Notify(src, string.format('Crafting failed. Refunded %d iron ingots.', refunded), 'error')
            LogCraftAttempt(src, weaponId, 1, false)
            TriggerClientEvent(Config.Events.client.craftResult, src, weaponId, false)
        end
    end)
end)

-- Remove the old assembly event since we now craft weapons directly

-- Exports
exports(Config.Events.exports.canUseBench, function(src, benchId)
    local bench = Config.Benches[benchId]
    if not bench then return false end
    return IsNearBench(src, bench) and HasPermission(src, bench)
end)

exports(Config.Events.exports.hasIronIngots, function(src, amount)
    return HasItem(src, 'ls_iron_ingot', amount)
end)

exports(Config.Events.exports.craftWeapon, function(src, weaponId)
    TriggerEvent(Config.Events.server.craftWeapon, weaponId)
end)

-- Admin commands (optional)
QBCore.Commands.Add('spawnbench', 'Spawn a crafting bench at current location (Admin Only)', {
    { name = 'benchname', help = 'Name for the bench (e.g., custom1, mybase, etc.)' },
    { name = 'benchtype', help = 'Type: all, rifle, pistol, smg, shotgun (optional, defaults to all)' }
}, true, function(source, args)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    -- Check admin permission (you can modify this check as needed)
    if not Player then
        Notify(src, 'Player not found', 'error')
        return
    end

    -- For now, allow anyone to place benches - you can add permission checks here
    -- if not Player.PlayerData.job.name == 'admin' then
    --     Notify(src, 'You do not have permission to use this command', 'error')
    --     return
    -- end

    local benchName = args[1]
    if not benchName then
        Notify(src, 'Please specify a bench name (e.g., /spawnbench mybase)', 'error')
        return
    end

    local benchType = args[2] or 'all'
    local playerCoords = GetEntityCoords(GetPlayerPed(src))
    local playerHeading = GetEntityHeading(GetPlayerPed(src))

    -- Define supported weapons based on type
    local supportedWeapons = {}
    if benchType == 'rifle' then
        supportedWeapons = { 'weapon_adv_rifle', 'weapon_sniper_frame' }
    elseif benchType == 'pistol' then
        supportedWeapons = { 'weapon_ap_pistol' }
    elseif benchType == 'smg' then
        supportedWeapons = { 'weapon_micro_smg', 'weapon_ap_smg' }
    elseif benchType == 'shotgun' then
        supportedWeapons = { 'weapon_compact_shotgun' }
    else
        -- 'all' or any other value
        supportedWeapons = { 'weapon_adv_rifle', 'weapon_ap_pistol', 'weapon_micro_smg', 'weapon_ap_smg', 'weapon_sniper_frame', 'weapon_compact_shotgun' }
    end

    -- Create bench ID
    local benchId = 'bench_' .. benchName

    -- Check if bench already exists
    if Config.Benches[benchId] then
        Notify(src, 'A bench with that name already exists!', 'error')
        return
    end

    -- Add bench to config dynamically
    Config.Benches[benchId] = {
        label = benchName:gsub("^%l", string.upper) .. ' Workbench',
        coords = { x = playerCoords.x, y = playerCoords.y, z = playerCoords.z, h = playerHeading },
        supported = supportedWeapons,
        hoverRadius = 1.0,
        markerType = 'circle',
        level = 1,
        propId = nil  -- Will store the prop entity ID
    }

    Notify(src, string.format('Bench "%s" spawned successfully!', benchName), 'success')
    Notify(src, string.format('Location: %.2f, %.2f, %.2f, %.2f',
        playerCoords.x, playerCoords.y, playerCoords.z, playerHeading), 'primary')

    Log('admin_spawnbench', src, {
        bench_id = benchId,
        bench_name = benchName,
        bench_type = benchType,
        coords = { x = playerCoords.x, y = playerCoords.y, z = playerCoords.z, h = playerHeading },
        supported = supportedWeapons
    })

    -- Spawn the prop if enabled
    if Config.Global.props.spawnProps then
        TriggerClientEvent('qb-crafting:spawnBenchProp', -1, benchId, {
            coords = { x = playerCoords.x, y = playerCoords.y, z = playerCoords.z, h = playerHeading },
            model = Config.Global.props.benchModel,
            label = Config.Benches[benchId].label
        })
    end

    -- Notify all clients to refresh bench locations
    TriggerClientEvent('qb-crafting:refreshBenches', -1)
end)

-- Remove bench command
QBCore.Commands.Add('removebench', 'Remove a crafting bench', {
    { name = 'benchname', help = 'Name of the bench to remove' }
}, true, function(source, args)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then
        Notify(src, 'Player not found', 'error')
        return
    end

    local benchName = args[1]
    if not benchName then
        Notify(src, 'Please specify a bench name (e.g., /removebench mybase)', 'error')
        return
    end

    local benchId = 'bench_' .. benchName

    if not Config.Benches[benchId] then
        Notify(src, 'Bench not found!', 'error')
        return
    end

    -- Remove the prop if it exists
    if Config.Global.props.spawnProps and Config.Global.props.deleteOnRemove then
        TriggerClientEvent('qb-crafting:removeBenchProp', -1, benchId)
    end

    -- Remove bench from config
    Config.Benches[benchId] = nil

    Notify(src, string.format('Bench "%s" removed successfully!', benchName), 'success')

    Log('admin_removebench', src, {
        bench_id = benchId,
        bench_name = benchName
    })

    -- Notify all clients to refresh bench locations
    TriggerClientEvent('qb-crafting:refreshBenches', -1)
end)

-- List all benches command
QBCore.Commands.Add('listbenches', 'List all crafting benches', {}, false, function(source, args)
    local src = source
    local benchList = {}

    for benchId, bench in pairs(Config.Benches) do
        table.insert(benchList, string.format('%s: %s (%.1f, %.1f, %.1f)',
            benchId, bench.label, bench.coords.x, bench.coords.y, bench.coords.z))
    end

    if #benchList > 0 then
        Notify(src, 'Available benches:', 'primary')
        for _, benchInfo in ipairs(benchList) do
            Notify(src, benchInfo, 'primary')
        end
    else
        Notify(src, 'No benches found!', 'error')
    end
end)

-- Emergency close command for testing
QBCore.Commands.Add('closecrafting', 'Force close crafting menu if stuck', {}, false, function(source, args)
    local src = source
    TriggerClientEvent('qb-crafting:forceCloseMenu', src)
    Notify(src, 'Crafting menu force closed', 'primary')
end)
