-- Main server-side logic for qb-crafting

local QBCore = exports['qb-core']:GetCoreObject()

-- Validate config on resource start
CreateThread(function()
    local isValid, errors = ValidateConfig()
    if not isValid then
        print('^1[qb-crafting] Configuration validation failed:^7')
        for _, error in ipairs(errors) do
            print('^1  - ' .. error .. '^7')
        end
        print('^1[qb-crafting] Please fix configuration errors before using the resource.^7')
    else
        print('^2[qb-crafting] Configuration validated successfully.^7')
    end
end)

-- Event: Request to open bench
RegisterNetEvent(Config.Events.server.requestOpenBench, function(benchId)
    local src = source
    
    -- Validate throttle
    if not ValidateThrottle(src, 'openBench') then
        LogSecurityViolation(src, 'throttle_violation', { action = 'openBench', bench = benchId })
        return
    end
    
    -- Validate bench exists
    local bench = Config.Benches[benchId]
    if not bench then
        LogSecurityViolation(src, 'invalid_bench', { bench = benchId })
        return
    end
    
    -- Validate distance
    if not IsNearBench(src, bench) then
        Notify(src, Config.UI.notNearBench, 'error')
        LogSecurityViolation(src, 'distance_violation', { bench = benchId })
        return
    end
    
    -- Validate permissions
    if not HasPermission(src, bench) then
        Notify(src, Config.UI.benchLocked, 'error')
        LogSecurityViolation(src, 'permission_violation', { bench = benchId })
        return
    end
    
    -- Log successful access
    LogBenchAccess(src, benchId)
    
    -- Build bench info and send to client
    local benchInfo = BuildBenchInfo(src, bench)
    benchInfo.benchId = benchId
    TriggerClientEvent(Config.Events.client.openMenu, src, benchId, benchInfo)
end)

-- Event: Start crafting component
RegisterNetEvent(Config.Events.server.startCraftComponent, function(recipeId, quantity)
    local src = source
    
    -- Validate throttle
    if not ValidateThrottle(src, 'startCraft') then
        LogSecurityViolation(src, 'throttle_violation', { action = 'startCraft', recipe = recipeId })
        return
    end
    
    -- Validate recipe exists
    local recipe = Config.ComponentRecipes[recipeId]
    if not recipe then
        LogSecurityViolation(src, 'invalid_recipe', { recipe = recipeId })
        return
    end
    
    -- Validate quantity
    quantity = math.max(1, math.min(tonumber(quantity) or 1, 50)) -- Cap at 50 for safety
    
    -- Check if recipe is batchable
    if quantity > 1 and not recipe.batchable then
        quantity = 1
    end
    
    -- Validate and remove inputs
    if not HasAndRemoveInputs(src, recipe.inputs, quantity) then
        Notify(src, Config.UI.missing, 'error')
        return
    end
    
    -- Notify client crafting started
    Notify(src, Config.UI.crafting, 'primary')
    
    -- Schedule crafting completion
    SetTimeout(recipe.craftTime * 1000 * quantity, function()
        local successCount = 0
        local failCount = 0
        
        -- Process each unit
        for i = 1, quantity do
            if math.random() <= recipe.successChance then
                -- Success
                if AddItem(src, recipeId, 1) then
                    successCount = successCount + 1
                    LogCraftAttempt(src, recipeId, 1, true)
                else
                    -- Failed to add item (inventory full?)
                    failCount = failCount + 1
                    LogError(src, 'inventory_full', { recipe = recipeId })
                end
            else
                -- Failure - give partial refund
                PartialRefund(src, recipe.inputs, Config.Global.craftFailRefund)
                failCount = failCount + 1
                LogCraftAttempt(src, recipeId, 1, false)
            end
        end
        
        -- Notify results
        if successCount > 0 then
            Notify(src, string.format('%s (x%d)', Config.UI.success, successCount), 'success')
            TriggerClientEvent(Config.Events.client.craftResult, src, recipeId, true, successCount)
        end
        
        if failCount > 0 then
            Notify(src, string.format('%s (x%d)', Config.UI.failed, failCount), 'error')
            TriggerClientEvent(Config.Events.client.craftResult, src, recipeId, false, failCount)
        end
    end)
end)

-- Event: Assemble blueprint
RegisterNetEvent(Config.Events.server.assembleBlueprint, function(blueprintId)
    local src = source
    
    -- Validate throttle
    if not ValidateThrottle(src, 'assemble') then
        LogSecurityViolation(src, 'throttle_violation', { action = 'assemble', blueprint = blueprintId })
        return
    end
    
    -- Validate blueprint exists
    local blueprint = Config.Blueprints[blueprintId]
    if not blueprint then
        LogSecurityViolation(src, 'invalid_blueprint', { blueprint = blueprintId })
        return
    end
    
    -- Validate bench exists
    local bench = Config.Benches[blueprint.bench]
    if not bench then
        LogError(src, 'invalid_bench_reference', { blueprint = blueprintId, bench = blueprint.bench })
        return
    end
    
    -- Validate distance
    if not IsNearBench(src, bench) then
        Notify(src, Config.UI.notNearBench, 'error')
        LogSecurityViolation(src, 'distance_violation', { action = 'assemble', bench = blueprint.bench })
        return
    end
    
    -- Validate permissions and bench level
    if not HasPermission(src, bench) then
        Notify(src, Config.UI.benchLocked, 'error')
        LogSecurityViolation(src, 'permission_violation', { action = 'assemble', bench = blueprint.bench })
        return
    end
    
    local requiredLevel = blueprint.requiredBenchLevel or 1
    if (bench.level or 1) < requiredLevel then
        Notify(src, Config.UI.benchLocked, 'error')
        LogSecurityViolation(src, 'bench_level_violation', { blueprint = blueprintId, required = requiredLevel, actual = bench.level })
        return
    end
    
    -- Validate and remove components
    if not RemoveItems(src, blueprint.requiredComponents) then
        Notify(src, Config.UI.missing, 'error')
        return
    end
    
    -- Notify client assembly started
    Notify(src, Config.UI.assembling, 'primary')
    
    -- Schedule assembly completion
    SetTimeout(blueprint.assembleTime * 1000, function()
        if AddItem(src, blueprint.finalItem, 1) then
            Notify(src, string.format('Assembled: %s', blueprint.label), 'success')
            LogAssemblyAttempt(src, blueprintId, true)
            TriggerClientEvent(Config.Events.client.assembleResult, src, blueprintId, true)
        else
            -- Failed to add item - return components
            for _, compId in ipairs(blueprint.requiredComponents) do
                AddItem(src, compId, 1)
            end
            Notify(src, 'Assembly failed - inventory full', 'error')
            LogError(src, 'assembly_inventory_full', { blueprint = blueprintId })
            TriggerClientEvent(Config.Events.client.assembleResult, src, blueprintId, false)
        end
    end)
end)

-- Exports
exports(Config.Events.exports.canUseBench, function(src, benchId)
    local bench = Config.Benches[benchId]
    if not bench then return false end
    return IsNearBench(src, bench) and HasPermission(src, bench)
end)

exports(Config.Events.exports.hasComponents, function(src, blueprintId)
    local blueprint = Config.Blueprints[blueprintId]
    if not blueprint then return false end
    return HasItems(src, blueprint.requiredComponents)
end)

exports(Config.Events.exports.assemble, function(src, blueprintId)
    TriggerEvent(Config.Events.server.assembleBlueprint, blueprintId)
end)

-- Admin commands (optional)
QBCore.Commands.Add('spawnbench', 'Spawn a crafting bench at current location (Admin Only)', {
    { name = 'benchname', help = 'Name for the bench (e.g., custom1, mybase, etc.)' },
    { name = 'benchtype', help = 'Type: all, rifle, pistol, smg, shotgun (optional, defaults to all)' }
}, true, function(source, args)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    -- Check admin permission (you can modify this check as needed)
    if not Player then
        Notify(src, 'Player not found', 'error')
        return
    end

    -- For now, allow anyone to place benches - you can add permission checks here
    -- if not Player.PlayerData.job.name == 'admin' then
    --     Notify(src, 'You do not have permission to use this command', 'error')
    --     return
    -- end

    local benchName = args[1]
    if not benchName then
        Notify(src, 'Please specify a bench name (e.g., /spawnbench mybase)', 'error')
        return
    end

    local benchType = args[2] or 'all'
    local playerCoords = GetEntityCoords(GetPlayerPed(src))
    local playerHeading = GetEntityHeading(GetPlayerPed(src))

    -- Define supported weapons based on type
    local supportedWeapons = {}
    if benchType == 'rifle' then
        supportedWeapons = { 'advanced_rifle', 'sniper_frame' }
    elseif benchType == 'pistol' then
        supportedWeapons = { 'ap_pistol' }
    elseif benchType == 'smg' then
        supportedWeapons = { 'micro_smg', 'ap_smg' }
    elseif benchType == 'shotgun' then
        supportedWeapons = { 'compact_shotgun' }
    else
        -- 'all' or any other value
        supportedWeapons = { 'advanced_rifle', 'ap_pistol', 'micro_smg', 'ap_smg', 'sniper_frame', 'compact_shotgun' }
    end

    -- Create bench ID
    local benchId = 'bench_' .. benchName

    -- Check if bench already exists
    if Config.Benches[benchId] then
        Notify(src, 'A bench with that name already exists!', 'error')
        return
    end

    -- Add bench to config dynamically
    Config.Benches[benchId] = {
        label = benchName:gsub("^%l", string.upper) .. ' Workbench',
        coords = { x = playerCoords.x, y = playerCoords.y, z = playerCoords.z, h = playerHeading },
        supported = supportedWeapons,
        hoverRadius = 1.0,
        markerType = 'circle',
        level = 1,
        propId = nil  -- Will store the prop entity ID
    }

    Notify(src, string.format('Bench "%s" spawned successfully!', benchName), 'success')
    Notify(src, string.format('Location: %.2f, %.2f, %.2f, %.2f',
        playerCoords.x, playerCoords.y, playerCoords.z, playerHeading), 'primary')

    Log('admin_spawnbench', src, {
        bench_id = benchId,
        bench_name = benchName,
        bench_type = benchType,
        coords = { x = playerCoords.x, y = playerCoords.y, z = playerCoords.z, h = playerHeading },
        supported = supportedWeapons
    })

    -- Spawn the prop if enabled
    if Config.Global.props.spawnProps then
        TriggerClientEvent('qb-crafting:spawnBenchProp', -1, benchId, {
            coords = { x = playerCoords.x, y = playerCoords.y, z = playerCoords.z, h = playerHeading },
            model = Config.Global.props.benchModel,
            label = Config.Benches[benchId].label
        })
    end

    -- Notify all clients to refresh bench locations
    TriggerClientEvent('qb-crafting:refreshBenches', -1)
end)

-- Remove bench command
QBCore.Commands.Add('removebench', 'Remove a crafting bench', {
    { name = 'benchname', help = 'Name of the bench to remove' }
}, true, function(source, args)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then
        Notify(src, 'Player not found', 'error')
        return
    end

    local benchName = args[1]
    if not benchName then
        Notify(src, 'Please specify a bench name (e.g., /removebench mybase)', 'error')
        return
    end

    local benchId = 'bench_' .. benchName

    if not Config.Benches[benchId] then
        Notify(src, 'Bench not found!', 'error')
        return
    end

    -- Remove the prop if it exists
    if Config.Global.props.spawnProps and Config.Global.props.deleteOnRemove then
        TriggerClientEvent('qb-crafting:removeBenchProp', -1, benchId)
    end

    -- Remove bench from config
    Config.Benches[benchId] = nil

    Notify(src, string.format('Bench "%s" removed successfully!', benchName), 'success')

    Log('admin_removebench', src, {
        bench_id = benchId,
        bench_name = benchName
    })

    -- Notify all clients to refresh bench locations
    TriggerClientEvent('qb-crafting:refreshBenches', -1)
end)

-- List all benches command
QBCore.Commands.Add('listbenches', 'List all crafting benches', {}, false, function(source, args)
    local src = source
    local benchList = {}

    for benchId, bench in pairs(Config.Benches) do
        table.insert(benchList, string.format('%s: %s (%.1f, %.1f, %.1f)',
            benchId, bench.label, bench.coords.x, bench.coords.y, bench.coords.z))
    end

    if #benchList > 0 then
        Notify(src, 'Available benches:', 'primary')
        for _, benchInfo in ipairs(benchList) do
            Notify(src, benchInfo, 'primary')
        end
    else
        Notify(src, 'No benches found!', 'error')
    end
end)
