Config = {}

-- Versioning & Feature Flags
Config.Meta = {
    version = '1.2.0',
    author = 'YourTeam',
    notes = 'Fictional, in-game crafting. No real-world instructions.',
}

-- === Base resource items (defaults you provided) ===
Config.Resources = {
    ['ls_copper_ingot'] = { label = 'Copper Ingot', weight = 250, stack = true, close = true, description = 'Refined copper ingot' },
    ['ls_iron_ingot']   = { label = 'Iron Ingot',   weight = 300, stack = true, close = true, description = 'Refined iron ingot' },
    ['ls_silver_ingot'] = { label = 'Silver Ingot', weight = 400, stack = true, close = true, description = 'Refined silver ingot' },
    ['ls_gold_ingot']   = { label = 'Gold Ingot',   weight = 500, stack = true, close = true, description = 'Refined gold ingot' },
}

-- === Component items (fictional) ===
Config.Components = {
    barrel_component = { item = 'comp_barrel', label = 'Barrel Component',    weight = 400, stack = true },
    body_component   = { item = 'comp_body',   label = 'Receiver Body',       weight = 600, stack = true },
    stock_component  = { item = 'comp_stock',  label = 'Stock',               weight = 350, stack = true },
    magazine_unit    = { item = 'comp_mag',    label = 'Magazine Unit',       weight = 200, stack = true },
    fire_control     = { item = 'comp_fire',   label = 'Fire Control Unit',   weight = 250, stack = true },
    small_parts      = { item = 'comp_small',  label = 'Small Parts Kit',     weight = 150, stack = true },
}

-- === Component recipes (inputs only use the 4 ingots by default) ===
Config.ComponentRecipes = {
    comp_barrel = {
        inputs = { ls_iron_ingot = 2, ls_copper_ingot = 1 },
        craftTime = 18, successChance = 0.96, batchable = true
    },
    comp_body = {
        inputs = { ls_iron_ingot = 3, ls_silver_ingot = 1 },
        craftTime = 25, successChance = 0.92, batchable = true
    },
    comp_stock = {
        inputs = { ls_copper_ingot = 2 },
        craftTime = 12, successChance = 0.98, batchable = true
    },
    comp_mag = {
        inputs = { ls_copper_ingot = 1, ls_iron_ingot = 1 },
        craftTime = 10, successChance = 0.99, batchable = true
    },
    comp_fire = {
        inputs = { ls_silver_ingot = 1, ls_gold_ingot = 1 },
        craftTime = 22, successChance = 0.90, batchable = true
    },
    comp_small = {
        inputs = { ls_silver_ingot = 1 },
        craftTime = 8, successChance = 0.99, batchable = true
    },
}

-- === Blueprints (fictional final items) ===
Config.Blueprints = {
    advanced_rifle = {
        label = 'Advanced Rifle', twoHanded = true, finalItem = 'weapon_adv_rifle',
        requiredComponents = { 'comp_barrel', 'comp_body', 'comp_stock', 'comp_mag', 'comp_fire', 'comp_small' },
        assembleTime = 20, bench = 'bench_rifle', requiredBenchLevel = 1
    },
    ap_pistol = {
        label = 'AP Pistol', twoHanded = false, finalItem = 'weapon_ap_pistol',
        requiredComponents = { 'comp_barrel', 'comp_body', 'comp_mag', 'comp_fire', 'comp_small' },
        assembleTime = 14, bench = 'bench_pistol', requiredBenchLevel = 1
    },
    micro_smg = {
        label = 'Micro SMG', twoHanded = false, finalItem = 'weapon_micro_smg',
        requiredComponents = { 'comp_barrel', 'comp_body', 'comp_mag', 'comp_small' },
        assembleTime = 12, bench = 'bench_smg', requiredBenchLevel = 1
    },
    ap_smg = {
        label = 'AP SMG', twoHanded = true, finalItem = 'weapon_ap_smg',
        requiredComponents = { 'comp_barrel', 'comp_body', 'comp_stock', 'comp_mag', 'comp_small' },
        assembleTime = 16, bench = 'bench_smg', requiredBenchLevel = 1
    },
    sniper_frame = {
        label = 'Sniper Frame', twoHanded = true, finalItem = 'weapon_sniper_frame',
        requiredComponents = { 'comp_barrel', 'comp_body', 'comp_stock', 'comp_fire' },
        assembleTime = 26, bench = 'bench_rifle', requiredBenchLevel = 2
    },
    compact_shotgun = {
        label = 'Compact Shotgun', twoHanded = true, finalItem = 'weapon_compact_shotgun',
        requiredComponents = { 'comp_barrel', 'comp_body', 'comp_stock', 'comp_small' },
        assembleTime = 18, bench = 'bench_shotgun', requiredBenchLevel = 1
    },
}

-- === Benches (7), placeable anywhere; includes your example coord ===
Config.Benches = {
    bench_rifle = {
        label = 'Rifle Workbench',
        coords = { x = 2943.4697, y = 2787.1169, z = 40.0063, h = 208.8873 },
        supported = { 'advanced_rifle', 'sniper_frame', 'ap_pistol' },
        hoverRadius = 1.0, markerType = 'circle', level = 1
    },
    bench_pistol = {
        label = 'Pistol Bench',
        coords = { x = 2443.00, y = 3000.50, z = 47.10, h = 90.0 },
        supported = { 'ap_pistol', 'micro_smg' },
        hoverRadius = 0.9, markerType = 'circle', level = 1
    },
    bench_smg = {
        label = 'SMG Bench',
        coords = { x = 2330.00, y = 3050.00, z = 48.00, h = 150.0 },
        supported = { 'micro_smg', 'ap_smg' },
        hoverRadius = 0.9, markerType = 'circle', level = 1
    },
    bench_shotgun = {
        label = 'Shotgun Bench',
        coords = { x = 1800.00, y = 2600.00, z = 45.00, h = 0.0 },
        supported = { 'compact_shotgun' },
        hoverRadius = 1.0, markerType = 'circle', level = 1
    },
    bench_light = {
        label = 'Light Weapons Bench',
        coords = { x = 2200.0, y = 2900.0, z = 42.0, h = 30.0 },
        supported = { 'micro_smg', 'ap_pistol' },
        hoverRadius = 0.8, markerType = 'circle', level = 1
    },
    bench_heavy = {
        label = 'Heavy Weapons Bench',
        coords = { x = 2600.0, y = 2800.0, z = 44.0, h = 270.0 },
        supported = { 'advanced_rifle', 'sniper_frame', 'compact_shotgun' },
        hoverRadius = 1.2, markerType = 'circle', level = 2
    },
    bench_mobile = {
        label = 'Mobile Bench (Portable)',
        coords = { x = 2400.0, y = 2700.0, z = 43.0, h = 90.0 },
        supported = { 'ap_pistol', 'micro_smg', 'compact_shotgun' },
        hoverRadius = 0.8, markerType = 'circle', level = 1
    },
    bench_custom = {
        label = 'Custom Workbench',
        coords = { x = 2965.6936, y = 2743.0601, z = 43.3666, h = 129.5918 },
        supported = { 'advanced_rifle', 'ap_pistol', 'micro_smg', 'ap_smg', 'compact_shotgun' },
        hoverRadius = 1.0, markerType = 'circle', level = 1
    },
}

-- === Global options & permissions ===
Config.Global = {
    openKey = 38,              -- E
    hoverKey = 18,             -- ALT
    altHoverDistance = 1.5,    -- meters
    craftFailRefund = 0.15,    -- fraction refunded on failure
    allowBatchCraft = true,
    requireJob = false,
    requiredJob = nil,         -- e.g. 'gunsmith'
    requireAce = false,        -- optional ace permission, e.g. 'crafting.use'
    aceName   = 'crafting.use',
    logEvents = true,
    throttle = {               -- Anti-spam per action (ms)
        openBench = 750,
        startCraft = 500,
        assemble = 750
    },
    distanceTolerance = 3.0,   -- must be near bench when starting/finishing
    inventory = {              -- inventory API naming (qb-core/QBox)
        add = 'AddItem',
        remove = 'RemoveItem',
        has = 'HasItem',
    },
}

-- === UI / Localization strings ===
Config.UI = {
    title = 'Bench Crafting',
    categories = { 'Guns', 'Ammo', 'Attachments', 'Components' },
    craftBtn = 'Craft',
    assembleBtn = 'Assemble',
    closeBtn = 'Close',
    closeHint = 'Hold ALT to highlight bench; press E to interact.',
    missing = 'Missing required items.',
    crafting = 'Crafting...',
    assembling = 'Assembling...',
    success = 'Crafting complete.',
    failed = 'Crafting failed.',
    benchLocked = 'You lack permission or bench level.',
    notNearBench = 'You moved too far from the bench.',
}

-- === Hook names (centralized) ===
Config.Events = {
    client = {
        openMenu = 'qb-crafting:openMenu',
        craftResult = 'qb-crafting:craftResult',
        assembleResult = 'qb-crafting:assembleResult',
    },
    server = {
        requestOpenBench = 'qb-crafting:requestOpenBench',
        startCraftComponent = 'qb-crafting:startCraftComponent',
        assembleBlueprint = 'qb-crafting:assembleBlueprint',
    },
    exports = {
        canUseBench = 'CanUseCraftBench',
        hasComponents = 'HasBlueprintComponents',
        assemble = 'AssembleBlueprint',
    }
}
