Config = {}

-- Versioning & Feature Flags
Config.Meta = {
    version = '1.2.0',
    author = 'YourTeam',
    notes = 'Fictional, in-game crafting. No real-world instructions.',
}

-- === Base resource items (only iron ingot needed for direct crafting) ===
Config.Resources = {
    ['ls_iron_ingot'] = { label = 'Iron Ingot', weight = 300, stack = true, close = true, description = 'Refined iron ingot used for weapon crafting' },
}

-- === Direct Weapon Crafting (iron ingots → weapons) ===
Config.Weapons = {
    weapon_ap_pistol = {
        label = 'AP Pistol',
        twoHanded = false,
        finalItem = 'weapon_ap_pistol',
        ironRequired = 8,  -- 8 iron ingots
        craftTime = 25,    -- 25 seconds
        bench = 'bench_pistol',
        requiredBenchLevel = 1,
        successChance = 0.95
    },
    weapon_micro_smg = {
        label = 'Micro SMG',
        twoHanded = false,
        finalItem = 'weapon_micro_smg',
        ironRequired = 12, -- 12 iron ingots
        craftTime = 30,    -- 30 seconds
        bench = 'bench_smg',
        requiredBenchLevel = 1,
        successChance = 0.92
    },
    weapon_ap_smg = {
        label = 'AP SMG',
        twoHanded = true,
        finalItem = 'weapon_ap_smg',
        ironRequired = 15, -- 15 iron ingots
        craftTime = 35,    -- 35 seconds
        bench = 'bench_smg',
        requiredBenchLevel = 1,
        successChance = 0.90
    },
    weapon_adv_rifle = {
        label = 'Advanced Rifle',
        twoHanded = true,
        finalItem = 'weapon_adv_rifle',
        ironRequired = 20, -- 20 iron ingots
        craftTime = 45,    -- 45 seconds
        bench = 'bench_rifle',
        requiredBenchLevel = 1,
        successChance = 0.88
    },
    weapon_sniper_frame = {
        label = 'Sniper Frame',
        twoHanded = true,
        finalItem = 'weapon_sniper_frame',
        ironRequired = 25, -- 25 iron ingots
        craftTime = 55,    -- 55 seconds
        bench = 'bench_rifle',
        requiredBenchLevel = 2,
        successChance = 0.85
    },
    weapon_compact_shotgun = {
        label = 'Compact Shotgun',
        twoHanded = true,
        finalItem = 'weapon_compact_shotgun',
        ironRequired = 18, -- 18 iron ingots
        craftTime = 40,    -- 40 seconds
        bench = 'bench_shotgun',
        requiredBenchLevel = 1,
        successChance = 0.87
    },
}

-- === Benches (7), placeable anywhere; includes your example coord ===
Config.Benches = {
    bench_rifle = {
        label = 'Rifle Workbench',
        coords = { x = 2943.4697, y = 2787.1169, z = 40.0063, h = 208.8873 },
        supported = { 'weapon_adv_rifle', 'weapon_sniper_frame', 'weapon_ap_pistol' },
        hoverRadius = 1.0, markerType = 'circle', level = 1
    },
    bench_pistol = {
        label = 'Pistol Bench',
        coords = { x = 2443.00, y = 3000.50, z = 47.10, h = 90.0 },
        supported = { 'weapon_ap_pistol', 'weapon_micro_smg' },
        hoverRadius = 0.9, markerType = 'circle', level = 1
    },
    bench_smg = {
        label = 'SMG Bench',
        coords = { x = 2330.00, y = 3050.00, z = 48.00, h = 150.0 },
        supported = { 'weapon_micro_smg', 'weapon_ap_smg' },
        hoverRadius = 0.9, markerType = 'circle', level = 1
    },
    bench_shotgun = {
        label = 'Shotgun Bench',
        coords = { x = 1800.00, y = 2600.00, z = 45.00, h = 0.0 },
        supported = { 'weapon_compact_shotgun' },
        hoverRadius = 1.0, markerType = 'circle', level = 1
    },
    bench_light = {
        label = 'Light Weapons Bench',
        coords = { x = 2200.0, y = 2900.0, z = 42.0, h = 30.0 },
        supported = { 'weapon_micro_smg', 'weapon_ap_pistol' },
        hoverRadius = 0.8, markerType = 'circle', level = 1
    },
    bench_heavy = {
        label = 'Heavy Weapons Bench',
        coords = { x = 2600.0, y = 2800.0, z = 44.0, h = 270.0 },
        supported = { 'weapon_adv_rifle', 'weapon_sniper_frame', 'weapon_compact_shotgun' },
        hoverRadius = 1.2, markerType = 'circle', level = 2
    },
    bench_mobile = {
        label = 'Mobile Bench (Portable)',
        coords = { x = 2400.0, y = 2700.0, z = 43.0, h = 90.0 },
        supported = { 'weapon_ap_pistol', 'weapon_micro_smg', 'weapon_compact_shotgun' },
        hoverRadius = 0.8, markerType = 'circle', level = 1
    },
    bench_custom = {
        label = 'Custom Workbench',
        coords = { x = 2965.6936, y = 2743.0601, z = 43.3666, h = 129.5918 },
        supported = { 'weapon_adv_rifle', 'weapon_ap_pistol', 'weapon_micro_smg', 'weapon_ap_smg', 'weapon_compact_shotgun' },
        hoverRadius = 1.0, markerType = 'circle', level = 1
    },
}

-- === Global options & permissions ===
Config.Global = {
    openKey = 38,              -- E
    hoverKey = 18,             -- ALT
    altHoverDistance = 1.5,    -- meters
    craftFailRefund = 0.15,    -- fraction refunded on failure
    allowBatchCraft = true,
    requireJob = false,
    requiredJob = nil,         -- e.g. 'gunsmith'
    requireAce = false,        -- optional ace permission, e.g. 'crafting.use'
    aceName   = 'crafting.use',
    logEvents = true,
    throttle = {               -- Anti-spam per action (ms)
        openBench = 750,
        startCraft = 500,
        assemble = 750
    },
    distanceTolerance = 3.0,   -- must be near bench when starting/finishing
    inventory = {              -- ox_inventory API for QBox
        useOxInventory = true, -- Set to true for ox_inventory, false for qb-inventory
        add = 'AddItem',
        remove = 'RemoveItem',
        has = 'HasItem',
    },
    -- Prop settings for visual benches
    props = {
        spawnProps = true,     -- Set to true to spawn physical bench props
        benchModel = 'prop_tool_bench02',  -- Default bench prop model
        deleteOnRemove = true, -- Delete props when bench is removed
    },
}

-- === UI / Localization strings ===
Config.UI = {
    title = 'Weapon Crafting',
    categories = { 'Guns' }, -- Only guns tab now
    craftBtn = 'Craft Weapon',
    closeBtn = 'Close',
    closeHint = 'Hold ALT to highlight bench; press E to interact.',
    missing = 'Missing required iron ingots.',
    crafting = 'Crafting weapon...',
    success = 'Weapon crafted successfully.',
    failed = 'Weapon crafting failed.',
    benchLocked = 'You lack permission or bench level.',
    notNearBench = 'You moved too far from the bench.',
    insufficientMaterials = 'You need %d iron ingots but only have %d.',
}

-- === Hook names (centralized) ===
Config.Events = {
    client = {
        openMenu = 'qb-crafting:openMenu',
        craftResult = 'qb-crafting:craftResult',
    },
    server = {
        requestOpenBench = 'qb-crafting:requestOpenBench',
        craftWeapon = 'qb-crafting:craftWeapon',
    },
    exports = {
        canUseBench = 'CanUseCraftBench',
        hasIronIngots = 'HasIronIngots',
        craftWeapon = 'CraftWeapon',
    }
}
