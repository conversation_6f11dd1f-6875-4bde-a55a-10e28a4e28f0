# QBox/QB-Core Crafting System

A complete, configurable, fictional in-game crafting system for QBox/qb-core servers featuring placeable benches, an intuitive ALT hover + E to use flow, and a fully data-driven pipeline from resources → components → assembly (weapons), plus optional ammo/attachments tabs.

## ⚠️ Safety & Policy

**This is in-game fiction only.** 
- Do not describe any real-world weapon manufacturing, tools, or safety procedures
- Keep component names abstract (e.g., barrel_component, receiver_body, fire_control_unit)
- Avoid instructions that enable real-world construction
- Treat everything as game items/IDs only

## 🎯 Success Criteria

- ✅ Benches render at arbitrary coords from config
- ✅ Holding ALT in range shows a hover circle + hint
- ✅ Pressing E opens the UI
- ✅ Menu shows categories: Guns, Ammo, Attachments, Components
- ✅ Crafting: Components consume resources (from config recipes only)
- ✅ Shows progress, success/fail rolls, partial refunds (configurable)
- ✅ Assembly: Validates required components server-side
- ✅ Grants a final weapon item ID (fictional)
- ✅ Permissions: Optional job/ace checks
- ✅ Logs all important actions when enabled
- ✅ Data-Driven: No hardcoded items. Everything lives in Config
- ✅ Default Recipes only use the 4 provided ingot IDs
- ✅ Extensible: Exports & events to plug in custom logic
- ✅ No duplication exploits: atomic inventory ops, server authority

## 📁 File Structure

```
resources/[local]/qb-crafting/
├─ fxmanifest.lua
├─ config.lua              -- All data lives here
├─ client/main.lua
├─ server/main.lua
├─ server/validators.lua   -- atomic checks, anti-dupe helpers
├─ server/logging.lua
├─ shared/contracts.lua    -- event names, exports, schema checks
├─ nui/                    -- Custom menu
│   ├─ index.html
│   ├─ app.js
│   └─ styles.css
└─ README.md
```

## 🚀 Installation

1. Download and place in your `resources/[local]/` folder
2. Add `ensure qb-crafting` to your server.cfg
3. Restart your server
4. Configure bench locations in `config.lua`

## ⚙️ Configuration

All configuration is done in `config.lua`. The system is completely data-driven:

### Adding New Resources
```lua
Config.Resources['my_new_ingot'] = { 
    label = 'My New Ingot', 
    weight = 300, 
    stack = true, 
    close = true, 
    description = 'A custom ingot' 
}
```

### Adding New Components
```lua
Config.Components.my_component = { 
    item = 'comp_my_item', 
    label = 'My Component', 
    weight = 400, 
    stack = true 
}

Config.ComponentRecipes.comp_my_item = {
    inputs = { my_new_ingot = 2, ls_iron_ingot = 1 },
    craftTime = 20, 
    successChance = 0.95, 
    batchable = true
}
```

### Adding New Blueprints
```lua
Config.Blueprints.my_weapon = {
    label = 'My Weapon', 
    twoHanded = true, 
    finalItem = 'weapon_my_weapon',
    requiredComponents = { 'comp_barrel', 'comp_body', 'comp_stock' },
    assembleTime = 25, 
    bench = 'bench_rifle', 
    requiredBenchLevel = 1
}

-- Add to bench's supported list
table.insert(Config.Benches['bench_rifle'].supported, 'my_weapon')
```

### Placing New Benches
```lua
Config.Benches.my_bench = {
    label = 'My Custom Bench',
    coords = { x = 123.0, y = 456.0, z = 78.0, h = 90.0 },
    supported = { 'my_weapon', 'ap_pistol' },
    hoverRadius = 1.0, 
    markerType = 'circle', 
    level = 1
}
```

## 🎮 Usage

### For Players
1. Approach any crafting bench
2. Hold **ALT** to see the interaction circle
3. Press **E** to open the crafting menu
4. Navigate between categories: Guns, Components, Ammo, Attachments
5. Click on items to see requirements and craft/assemble

### For Admins
- Use `/placebench <type>` to get coordinates for bench placement
- Configure permissions via job requirements or ACE permissions
- Monitor all crafting activity through comprehensive logging

## 🔧 Exports

### Server Exports
```lua
-- Check if player can use a specific bench
local canUse = exports['qb-crafting']:CanUseCraftBench(source, 'bench_rifle')

-- Check if player has required components for a blueprint
local hasComponents = exports['qb-crafting']:HasBlueprintComponents(source, 'advanced_rifle')

-- Trigger assembly programmatically
exports['qb-crafting']:AssembleBlueprint(source, 'advanced_rifle')
```

## 🛡️ Security Features

- **Server-authoritative**: All inventory operations happen server-side
- **Throttling**: Prevents spam/exploit attempts
- **Distance validation**: Players must stay near benches
- **Atomic transactions**: No item duplication possible
- **Permission system**: Job and ACE permission support
- **Comprehensive logging**: All actions are logged for monitoring

## 📊 Default Configuration

### Benches (7 total)
- **bench_rifle**: Advanced rifles, sniper frames, AP pistols
- **bench_pistol**: AP pistols, micro SMGs
- **bench_smg**: Micro SMGs, AP SMGs
- **bench_shotgun**: Compact shotguns
- **bench_light**: Light weapons (pistols, micro SMGs)
- **bench_heavy**: Heavy weapons (rifles, snipers, shotguns)
- **bench_mobile**: Portable bench for multiple weapon types

### Default Resources
- `ls_copper_ingot` - Copper Ingot
- `ls_iron_ingot` - Iron Ingot  
- `ls_silver_ingot` - Silver Ingot
- `ls_gold_ingot` - Gold Ingot

### Components
- `comp_barrel` - Barrel Component
- `comp_body` - Receiver Body
- `comp_stock` - Stock
- `comp_mag` - Magazine Unit
- `comp_fire` - Fire Control Unit
- `comp_small` - Small Parts Kit

### Blueprints
- `advanced_rifle` - Advanced Rifle (2H)
- `ap_pistol` - AP Pistol (1H)
- `micro_smg` - Micro SMG (1H)
- `ap_smg` - AP SMG (2H)
- `sniper_frame` - Sniper Frame (2H)
- `compact_shotgun` - Compact Shotgun (2H)

## 🔄 Events

### Client Events
- `qb-crafting:openMenu` - Opens the crafting menu
- `qb-crafting:craftResult` - Receives craft completion results
- `qb-crafting:assembleResult` - Receives assembly completion results

### Server Events
- `qb-crafting:requestOpenBench` - Request to open a bench
- `qb-crafting:startCraftComponent` - Start crafting a component
- `qb-crafting:assembleBlueprint` - Start assembling a blueprint

## 🐛 Troubleshooting

### Common Issues

**Menu won't open:**
- Check if you're within the hover distance (default 1.5m)
- Verify you have the required job/permissions
- Check server console for permission violations

**Crafting fails:**
- Ensure you have all required materials
- Check success chance in component recipes
- Verify inventory isn't full

**Items not appearing:**
- Validate your config with the built-in validation
- Check server console for configuration errors
- Ensure all referenced items exist in your item database

## 📝 Logging

All crafting activities are logged when `Config.Global.logEvents = true`:

- Bench access attempts
- Craft attempts (success/failure)
- Assembly attempts
- Security violations
- Permission violations
- Distance violations

## 🔮 Future Extensions

The system is designed to be easily extensible:

- **Progression System**: Add blueprint unlock requirements
- **Quality System**: Random durability/metadata on crafted items
- **Admin Tools**: In-game bench placement commands
- **Telemetry**: Success rate analytics for balancing
- **Achievements**: Integration with achievement systems

## 📄 License

This resource is provided as-is for educational and entertainment purposes only. All weapon names and components are fictional and for in-game use only.

## 🤝 Support

For support, configuration help, or feature requests, please refer to your server's development team or community forums.

---

**Remember: This is a fictional crafting system for entertainment purposes only. No real-world weapon manufacturing information is provided or intended.**
